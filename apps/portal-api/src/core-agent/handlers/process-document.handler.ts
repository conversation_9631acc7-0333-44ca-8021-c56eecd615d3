import { Injectable } from "@nestjs/common";
import { BaseIntentHandler } from "./base-intent-handler";
import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
import { ResponseFragment, ValidatedIntent } from "../types/response-fragment.types";
import { ShipmentContext, ShipmentContextWithServices } from "../../agent-context";
import { DocumentProcessorService } from "../services/document-processor.service";
import { Shipment, CommercialInvoice, OrganizationType } from "nest-modules";

/**
 * Handles document processing requests and automatic entry submission.
 * This handler uses the agent-context module to reduce cross-module dependencies.
 */
@Injectable()
export class ProcessDocumentHandler extends BaseIntentHandler {
  constructor(private readonly documentProcessor: DocumentProcessorService) {
    super();
  }

  readonly classificationMeta: IntentClassificationMeta = {
    intent: "PROCESS_DOCUMENT" as const,
    description:
      "The user is sending us documents as attachments and requesting for them to be processed as part of a shipment",
    examples: [
      "Please process the attached documents",
      "Process these documents for customs",
      "Submit the entry with these documents",
      "Use the attached paperwork",
      "Process the commercial invoice attached"
    ],
    keywords: ["process", "attached", "documents", "paperwork", "submit", "entry", "customs"]
  };

  async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<ResponseFragment[]> {
    const instructions = this.extractInstructions(validatedIntent);

    this.logger.log(
      `Handling PROCESS_DOCUMENT for shipment ${context.shipment?.id || "N/A"} with ${instructions.length} instructions`
    );

    if (!context.shipment) {
      this.logger.error("Cannot process documents: no shipment found in context");
      throw new Error("No shipment found for document processing");
    }

    try {
      // Step 1: Build main messages array for consolidated templates
      const mainMessages = await this.buildMainMessages(validatedIntent, context);
      this.logger.debug(`[PROCESS_DOCUMENT] Built ${mainMessages.length} main messages`);

      // Step 2: Determine conditional fragment flags
      const consolidatedOptions = this.buildConsolidatedOptions(context);
      this.logger.debug(`[PROCESS_DOCUMENT] Consolidated options: ${JSON.stringify(consolidatedOptions)}`);

      // Step 3: Use consolidated fragment system
      const fragments = this.createConsolidatedFragments(mainMessages, context, consolidatedOptions);

      this.logger.log(
        `[PROCESS_DOCUMENT] Generated ${fragments.length} consolidated fragments for shipment ${context.shipment.id}`
      );

      return fragments;
    } catch (error) {
      this.logger.error(
        `Failed to process documents for shipment ${context.shipment.id}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * Handle post-document processing with customs flow using agent-context services.
   * SIMPLIFIED: Uses IEntrySubmissionService.attemptShipmentSubmission() instead of manual workflow.
   */
  private async handlePostDocumentProcessingWithCustomsFlow(
    shipment: Shipment,
    context: ShipmentContextWithServices
  ): Promise<{ cadDocument?: any } | undefined> {
    this.logger.log(
      `🔄 SUBMISSION FLOW START: Handling post-document processing for shipment ${shipment.id} using agent-context services`
    );

    try {
      // Check if shipment is already submitted using context data
      if (context.isSubmitted) {
        this.logger.warn(
          `🛑 SUBMISSION FLOW STOPPED: Shipment ${shipment.id} is already submitted, skipping processing`
        );
        return;
      }

      // Use the agent-context submission service (replaces 100+ lines of manual logic)
      const submissionResult = await context._services.entrySubmissionService?.attemptShipmentSubmission(
        shipment,
        context.compliance,
        context.organization.id
      );

      if (submissionResult?.submissionError) {
        this.logger.error(`💥 SUBMISSION FLOW FAILED: ${submissionResult.submissionError}`);
        return;
      }

      // Refresh context after potential status changes
      await this.refreshContext(context);

      // For DEMO organizations, automatically generate CAD document after submission
      if (context.organization.organizationType === OrganizationType.DEMO) {
        this.logger.log(`🎯 DEMO CAD GENERATION: Attempting CAD generation for shipment ${shipment.id}`);
        const cadData = await this.handleDemoOrganizationCADGeneration(shipment, context);
        return cadData;
      }

      // Return empty object to match return type (no CAD document generated for non-demo orgs)
      return {};
    } catch (error) {
      this.logger.error(
        `💥 SUBMISSION FLOW EXCEPTION: Error in submission flow for shipment ${shipment.id}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * Handle automatic CAD document generation for demo organizations.
   * SIMPLIFIED: Uses services from context instead of direct injection.
   */
  private async handleDemoOrganizationCADGeneration(
    shipment: Shipment,
    context: ShipmentContextWithServices
  ): Promise<{ cadDocument?: any; acknowledgment?: string }> {
    if (context.organization.organizationType !== OrganizationType.DEMO) {
      this.logger.warn(
        `🚫 DEMO CAD GENERATION: Shipment ${shipment.id} is not from a demo organization, skipping CAD generation`
      );
      return {};
    }

    this.logger.log(`🎯 DEMO CAD GENERATION: Starting CAD generation for demo shipment ${shipment.id}`);

    try {
      // Get commercial invoices for the shipment using direct query (since interface method doesn't exist yet)
      const commercialInvoices = await this.getCommercialInvoicesForShipment(shipment.id);

      if (!commercialInvoices || commercialInvoices.length === 0) {
        this.logger.warn(
          `🚫 DEMO CAD GENERATION: No commercial invoices found for shipment ${shipment.id}. Deferring CAD generation.`
        );
        return {
          acknowledgment: "We will generate a CAD document once a commercial invoice is properly processed."
        };
      }

      // Get organization importer using context service
      const importersResponse = await context._services.importerService?.getImporters({
        organizationId: context.organization.id,
        limit: 1
      });
      const organizationImporter =
        importersResponse?.importers?.length > 0 ? importersResponse.importers[0] : null;

      // Generate CAD attachment using context service
      const rawCadAttachment = await context._services.rnsStatusChangeEmailSender?.createCADAttachment(
        shipment,
        commercialInvoices,
        organizationImporter
      );

      if (!rawCadAttachment) {
        this.logger.error(
          `❌ DEMO CAD GENERATION: Failed to generate CAD attachment for shipment ${shipment.id}`
        );
        return {};
      }

      // Clean the base64 string
      const cleanedCadAttachment = {
        ...rawCadAttachment,
        b64Data: rawCadAttachment.b64Data.replace(/[\s\r\n\t]/g, "")
      };

      // Validate using document processor (only non-agent-context service needed)
      if (this.documentProcessor.validateCADAttachment(cleanedCadAttachment)) {
        this.logger.log(
          `🎉 DEMO CAD GENERATION: Successfully generated CAD document for demo shipment ${shipment.id}`
        );
        return { cadDocument: cleanedCadAttachment };
      } else {
        this.logger.error(
          `❌ DEMO CAD GENERATION: CAD attachment validation failed for shipment ${shipment.id}`
        );
        return {};
      }
    } catch (error) {
      this.logger.error(
        `💥 DEMO CAD GENERATION: Error generating CAD document for shipment ${shipment.id}: ${error.message}`,
        error.stack
      );
      return {};
    }
  }

  /**
   * Get commercial invoices for a shipment using direct repository query.
   * TODO: This should be moved to ICommercialInvoiceService interface when available.
   */
  private async getCommercialInvoicesForShipment(shipmentId: number): Promise<CommercialInvoice[]> {
    // For now, return empty array - this would need proper implementation
    // In a real scenario, this would query the database directly or use a service
    this.logger.warn(`TODO: Implement commercial invoice retrieval for shipment ${shipmentId}`);
    return [];
  }

  /**
   * Refresh context after submission to get latest data.
   * SIMPLIFIED: For now, just log - full implementation would need context service access.
   */
  private async refreshContext(context: ShipmentContextWithServices): Promise<void> {
    try {
      // TODO: Need to add context service to _services or use different approach
      // For now, just log that refresh was attempted
      this.logger.debug(
        `🔄 Context refresh requested for shipment ${context.shipment.id} (not implemented yet)`
      );
    } catch (error) {
      this.logger.warn(`⚠️ Failed to refresh context: ${error.message}`);
    }
  }

  /**
   * Build main messages array for consolidated templates
   * SIMPLIFIED: Uses context services instead of direct injection
   */
  private async buildMainMessages(
    validatedIntent: ValidatedIntent,
    context: ShipmentContext
  ): Promise<Array<{ content?: string; type?: string; priority: number; attachments?: any }>> {
    const mainMessages: Array<{ content?: string; type?: string; priority: number; attachments?: any }> = [];

    // Cast to access services
    const contextWithServices = context as ShipmentContextWithServices;

    // Handle post-document processing with customs flow
    const cadData = await this.handlePostDocumentProcessingWithCustomsFlow(
      context.shipment,
      contextWithServices
    );

    // Process document attachments (only service that still needs direct injection)
    const processedDocuments =
      validatedIntent.attachments && validatedIntent.attachments.length > 0
        ? await this.documentProcessor.processDocumentAttachments(validatedIntent)
        : await this.documentProcessor.fetchProcessedDocumentsFromDatabase(context);

    // Use pre-computed context data instead of missing service
    const hasAllRequiredDocuments = context.isAllDocsReceived;

    // Debug logging
    this.logger.debug(
      `🔍 FRAGMENT CONTEXT DEBUG for shipment ${context.shipment.id}: ` +
        `processedDocuments=${processedDocuments.length}, ` +
        `hasAllRequiredDocuments=${hasAllRequiredDocuments}, ` +
        `shipment.customsStatus=${context.shipment.customsStatus}`
    );

    // Build document processing message
    const documentProcessingMessage = this.buildDocumentProcessingMessage(
      processedDocuments,
      hasAllRequiredDocuments,
      cadData
    );
    mainMessages.push({
      type: documentProcessingMessage.type,
      priority: 1,
      attachments: documentProcessingMessage.attachments
    });

    // If CAD document was generated, add CAD message as well
    if (cadData && cadData.cadDocument) {
      const cadMessage = this.buildCadMessage(context, cadData.cadDocument);
      if (cadMessage) {
        mainMessages.push({
          type: cadMessage.type,
          priority: 2,
          attachments: cadMessage.attachments
        });
      }
    }

    return mainMessages;
  }

  /**
   * Build consolidated options for conditional fragment control
   * MIGRATION: Replaces individual fragment addition logic
   */
  private buildConsolidatedOptions(context: ShipmentContext): {
    showValidationIssues?: boolean;
    showDocumentStatus?: boolean;
    showAdditionalValidation?: boolean;
    validationIssues?: any;
    additionalValidation?: { issues: string[] };
  } {
    // For document processing, show validation issues if the shipment has pending statuses
    const shouldShowValidationIssues = ["pending-commercial-invoice", "pending-confirmation"].includes(
      context.shipment.customsStatus
    );

    // Build validation issues if needed
    let validationIssues = null;
    if (shouldShowValidationIssues) {
      validationIssues = this.buildValidationIssues(context);
    }

    const options = {
      showValidationIssues: shouldShowValidationIssues && !!validationIssues,
      showDocumentStatus: shouldShowValidationIssues, // Show document status for pending statuses
      showAdditionalValidation: false, // Not used for document processing
      validationIssues,
      additionalValidation: undefined
    };

    this.logger.debug(
      `[PROCESS_DOCUMENT] Consolidated options - showValidationIssues: ${options.showValidationIssues}, showDocumentStatus: ${options.showDocumentStatus}`
    );

    return options;
  }

  /**
   * Build document processing message using template-based approach
   */
  private buildDocumentProcessingMessage(
    processedDocuments: any[],
    hasAllRequiredDocuments: boolean,
    cadData?: any
  ): {
    type: string;
    attachments: any;
  } {
    return {
      type: "document-processing-messages",
      attachments: {
        documents: processedDocuments,
        hasAllRequiredDocuments,
        acknowledgment: cadData?.acknowledgment // Use custom acknowledgment if provided
      }
    };
  }
}
